# 性能优化配置详解

本文档详细介绍 Nginx 性能优化的各项配置和最佳实践。

## 高并发优化配置

### 完整配置示例

```nginx
server {
    # 高并发优化选项
    gzip_static on; # 提供预压缩的静态文件
    open_file_cache max=10000 inactive=30s; # 缓存文件句柄和元数据
    sendfile on; # 启用零拷贝文件传输
    tcp_nopush on; # 优化 TCP 包发送

    # 统一资源缓存策略
    include snippets/static-cache.conf;
}
```

### 配置参数详解

| 指令 | 功能 | 工作原理 | 适用场景 & 注意点 |
|------|------|---------|------------------|
| **`gzip_static on;`** | 如果客户端在 `Accept-Encoding` 里声明支持 `gzip`，且磁盘上存在同名文件 `xxx.css.gz`，Nginx 直接把该压缩文件发给客户端，并在响应头加 `Content-Encoding: gzip`。**不再临时压缩**。 | 把"压缩"这件 CPU 密集型工作前移到构建阶段（CI/CD or webpack/rollup 等），运行时仅做**零开销的文件读取**。 | - 前提：你已在打包流程里生成 `.gz` 文件（或更优的 Brotli）。<br>- 不对动态请求生效（如 PHP/FastCGI）。<br>- 若启用 HTTP/2 + TLS，浏览器仍愿意收 gzip；若你想给新浏览器用 Brotli，可在同目录放 `.br` 并用 `brotli_static on;`（需编译模块或用官方 `nginx:mainline-alpine`）。 |
| **`open_file_cache max=10000 inactive=30s;`** | 让 Nginx 把最近访问过的文件句柄、大小、时间戳等元数据放进共享缓存：<br>• *最多* 缓 10000 项；<br>• 若 30 秒内未再访问，则从缓存淘汰。 | Linux 打开同一文件需要系统调用 `open()` + `stat()`；在热点静态站点里，这部分可能占到**几十% 的 CPU**。缓存后，Nginx 直接复用文件描述符或元数据，少一次或多次系统调用。 | - "10000" 并不大，占用内存 ~1–2 MB，可按站点热文件数酌情加大。<br>- 只建议对静态文件目录开启；如果你有大量短暂文件（如上传存储）可单独写在 `location` 块里排除缓存。 |
| **`sendfile on;`** | 启用 Linux `sendfile(2)` 零拷贝：从磁盘缓存页直接写入套接字缓冲区，不再绕经用户态。 | 普通流程：磁盘 → 内核→ 用户态 (Nginx) → 再回内核 → NIC。<br>启用后：磁盘缓存页地址直接映射到 NIC DMA，省掉两次内核/用户拷贝。 | - 对大文件（图片、视频、JS/CSS）效果最明显。<br>- 不会影响动态 upstream 响应（它们本来是在内存中）。<br>- 若使用 SSL，需要内核 4.6+ 的 `sendfile` + `TLS offload`，否则 Nginx 会自动降级为 `writev()`；现代发行版/官方 Nginx 镜像都已支持。 |
| **`tcp_nopush on;`** | 与 `sendfile` 配套：在 **发送最后一个包之前**，内核尽量把响应头和若干文件数据**拼成一个满大小 MSS 报文**再发，减少 TCP 包数量。 | 利用 Linux `TCP_CORK` 套接字选项：先"塞住" (cork) 输出，让内核聚合数据；当缓冲区满或关闭时"一次性"发出。 | - 只有当 `sendfile on` 时才建议启用。<br>- 对启用了 **HTTP/2** 的端口无影响（HTTP/2 在同一个 TLS 流里会做更高级的帧合并）。<br>- 极少数对极低延迟敏感的长轮询/实时应用可考虑关闭。 |

### 使用场景建议

* **静态文件量大、QPS 高（博客、前端 SPA、图片站）** → 建议全部开启
* **主要返回动态 HTML/JSON**（压缩仍由 `gzip on` 或应用层完成） → `open_file_cache` 收益有限，可只用 `sendfile on`
* **有条件的现代部署** → 再加上 `http2`、`ssl_reuse_buffers on;` 以及 Brotli/Binary AST 等手段，可把带宽与延迟进一步压缩

### 使用小结

这四行配置把"文件 IO、压缩、封包"的成本前置或转移到内核零拷贝路径，把宝贵的 CPU 留给真正动态的业务逻辑，是 Nginx 在静态内容高并发场景下的经典性能基线。

## 缓存策略配置

### `snippets/static-cache.conf`

```nginx
# 先按扩展名映射不同过期时间
map $uri $static_expires {
    default         0;      # 默认不缓存或极短缓存 (例如 API 动态内容)
    ~*\\.(html?|json)$  5m;   # HTML 和 JSON 文件缓存5分钟
    ~*\\.css$        7d;   # CSS 文件缓存7天
    ~*\\.js$         7d;   # JavaScript 文件缓存7天
    ~*\\.(ico|woff2?|ttf|eot|svg)$ 30d; # 图标和字体文件缓存30天
    ~*\\.(jpe?g|png|gif|bmp|webp)$ 30d; # 图片文件缓存30天
}

location / {
    try_files $uri $uri/ =404; # 对于目录，尝试 $uri/index.html (如果 autoindex off)
    expires $static_expires;
    etag    on; # 开启 ETag，配合 Cache-Control 实现更精细的缓存验证
}
```

### 缓存策略说明

| 文件类型 | 缓存时间 | 原因 |
|----------|----------|------|
| **HTML, JSON** | 5分钟 | 内容可能频繁更新，短缓存保证内容时效性 |
| **CSS, JS** | 7天 | 通常在版本发布时才更新，可以较长缓存 |
| **图标, 字体** | 30天 | 很少变动的资源，可以长期缓存 |
| **图片** | 30天 | 大文件，长期缓存减少带宽消耗 |

### 高级缓存配置

```nginx
# 更精细的缓存控制
location ~* \.(css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    
    # 对于带版本号的资源，可以设置更长的缓存
    if ($args ~ "v=|version=") {
        expires max;
        add_header Cache-Control "public, immutable";
    }
}

# 动态内容的缓存控制
location /api/ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
}
```

## Gzip 压缩优化

### 基础 Gzip 配置

```nginx
# 在 http 块中配置
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;
```

### Brotli 压缩（更优选择）

```nginx
# 需要编译 brotli 模块
brotli on;
brotli_static on;
brotli_comp_level 6;
brotli_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;
```

## 连接优化

### Keep-Alive 配置

```nginx
# 在 http 块中配置
keepalive_timeout 65;
keepalive_requests 1000;

# 对于上游连接
upstream backend {
    server 127.0.0.1:8080;
    keepalive 32;  # 保持 32 个连接
}

location / {
    proxy_pass http://backend;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
}
```

### 工作进程优化

```nginx
# 在 main 块中配置
worker_processes auto;  # 自动设置为 CPU 核心数
worker_connections 1024;  # 每个进程的最大连接数

# 事件驱动模型
events {
    use epoll;  # Linux 下推荐
    multi_accept on;
}
```

## 缓冲区优化

### 客户端缓冲区

```nginx
# 在 http 块中配置
client_body_buffer_size 128k;
client_max_body_size 10m;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;
```

### 代理缓冲区

```nginx
# 在 location 块中配置
proxy_buffering on;
proxy_buffer_size 4k;
proxy_buffers 8 4k;
proxy_busy_buffers_size 8k;
proxy_temp_file_write_size 8k;
```

## 日志优化

### 高性能日志配置

```nginx
# 使用缓冲写入减少磁盘 I/O
access_log /var/log/nginx/access.log main buffer=32k flush=5s;

# 对于高流量站点，可以考虑采样记录
map $status $loggable {
    ~^[23]  0;  # 不记录 2xx 和 3xx
    default 1;
}

access_log /var/log/nginx/error_only.log main if=$loggable;
```

### 自定义日志格式

```nginx
log_format performance '$remote_addr - $remote_user [$time_local] '
                      '"$request" $status $body_bytes_sent '
                      '"$http_referer" "$http_user_agent" '
                      'rt=$request_time uct="$upstream_connect_time" '
                      'uht="$upstream_header_time" urt="$upstream_response_time"';
```

## HTTP/2 优化

### HTTP/2 推送

```nginx
server {
    listen 443 ssl http2;
    
    # 预推送关键资源
    location = /index.html {
        http2_push /static/css/main.css;
        http2_push /static/js/main.js;
    }
    
    # 设置推送策略
    http2_push_preload on;
}
```

### HTTP/2 特定优化

```nginx
# 在 http 块中配置
http2_max_field_size 16k;
http2_max_header_size 32k;
http2_max_requests 1000;
```

## 监控和调试

### 状态监控

```nginx
# 启用状态模块
location /nginx_status {
    stub_status on;
    allow 127.0.0.1;
    deny all;
}
```

### 性能指标收集

```nginx
# 详细的性能日志
log_format perf '$remote_addr - $remote_user [$time_local] '
                '"$request" $status $body_bytes_sent '
                'rt=$request_time '
                'ua="$upstream_addr" '
                'us="$upstream_status" '
                'ut="$upstream_response_time" '
                'ul="$upstream_response_length" '
                '"$http_user_agent"';

access_log /var/log/nginx/performance.log perf;
```

## 系统级优化

### 文件描述符限制

```bash
# 在系统级别设置
echo 'fs.file-max = 65536' >> /etc/sysctl.conf

# 为 nginx 用户设置限制
echo 'nginx soft nofile 65536' >> /etc/security/limits.conf
echo 'nginx hard nofile 65536' >> /etc/security/limits.conf
```

### TCP 优化

```bash
# 在 /etc/sysctl.conf 中添加
net.core.somaxconn = 1024
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 1024
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1800
```

## 完整性能配置示例

### nginx.conf 主配置

```nginx
user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # 基础设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
    
    # 缓冲区设置
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    # 包含站点配置
    include /etc/nginx/sites-enabled/*;
}
```

### 高性能站点配置

```nginx
server {
    listen 443 ssl http2;
    server_name example.com;
    
    # SSL 配置
    include snippets/ssl-params.conf;
    
    # 性能优化
    gzip_static on;
    open_file_cache max=10000 inactive=30s;
    open_file_cache_valid 60s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    
    # 缓存策略
    include snippets/static-cache.conf;
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # 日志（使用缓冲）
    access_log /var/log/nginx/example.com.log main buffer=32k flush=5s;
    
    location / {
        root /var/www/example.com;
        index index.html;
        
        # 文件缓存
        expires $static_expires;
        etag on;
    }
}
```

## 性能测试与验证

### 压力测试工具

```bash
# 使用 wrk 进行压力测试
wrk -t12 -c400 -d30s --latency http://example.com/

# 使用 ab 测试
ab -n 10000 -c 100 http://example.com/

# 使用 hey 测试
hey -n 10000 -c 100 http://example.com/
```

### 性能监控脚本

```bash
#!/bin/bash
# 监控 Nginx 性能指标
while true; do
    echo "$(date): Active connections: $(curl -s http://localhost/nginx_status | grep Active | awk '{print $3}')"
    sleep 10
done
```

## 配置总结

性能优化的核心要点：

✅ **静态文件优化**: `sendfile`、`gzip_static`、文件缓存
✅ **连接管理**: Keep-Alive、连接池、多路复用
✅ **缓存策略**: 基于文件类型的差异化缓存
✅ **压缩优化**: Gzip/Brotli 压缩减少传输大小
✅ **缓冲区调优**: 合理设置各类缓冲区大小
✅ **日志优化**: 使用缓冲减少磁盘 I/O
✅ **系统调优**: 文件描述符、TCP 参数优化

这些配置可以显著提升 Nginx 在高并发场景下的性能表现。 