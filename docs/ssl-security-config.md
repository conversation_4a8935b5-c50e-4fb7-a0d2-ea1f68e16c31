# SSL 安全配置详解

本文档详细介绍 Nginx SSL/TLS 安全配置的各项参数和最佳实践。

## 完整 SSL 配置文件

### `snippets/ssl-params.conf`

```nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers   'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256';
ssl_prefer_server_ciphers on;
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:50m;
ssl_session_tickets off;

add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
add_header X-Content-Type-Options "nosniff";
add_header X-Frame-Options "SAMEORIGIN";
add_header Referrer-Policy "strict-origin-when-cross-origin";
```

## 配置参数详解

### SSL/TLS 协议配置

| 指令 | 功能 | 配置说明 |
|------|------|----------|
| `ssl_protocols TLSv1.2 TLSv1.3;` | 仅允许 TLS 1.2 与 1.3 | **淘汰旧版协议**：TLS 1.0/1.1 在 2021 年起就被主要浏览器和云厂商全面弃用，残留价值只在于极老旧客户端。关闭它们可防御 BEAST、POODLE 等已知攻击。<br>**保留 TLS 1.2**：仍覆盖 >99% 客户端（含 Android 7、Win 7/8/10 旧版 IE）。<br>**开启 TLS 1.3**：更快握手、内建前向保密 (PFS)、固定安全套件，已由所有现代浏览器支持。 |

### 密码套件配置

| 指令 | 功能 | 配置说明 |
|------|------|----------|
| `ssl_ciphers 'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256';` | 指定可用的 **TLS 1.3** 密码套件顺序 | TLS 1.3 只允许四个官方套件，里头去掉不常用的 `TLS_AES_128_CCM_*`，留下 3 个主流：<br>• `TLS_AES_256_GCM_SHA384` —— GCM + SHA-384，适合硬件加速<br>• `TLS_CHACHA20_POLY1305_SHA256` —— 移动端无 AES-NI 时更快<br>• `TLS_AES_128_GCM_SHA256` —— 与上兼容、密钥更短<br>顺序对 TLS 1.3 实际无影响（浏览器自选），但写明能让配置更显式，也同时约束 **TLS 1.2** 的协商范围。 |
| `ssl_prefer_server_ciphers on;` | 对 **TLS 1.2 及以下** 强制使用服务器端给出的套件优先级 | 阻止客户端选到弱套件（如 3DES）。TLS 1.3 协议层自带固定套件列表，不受此指令影响。 |

### 会话管理配置

| 指令 | 功能 | 配置说明 |
|------|------|----------|
| `ssl_session_timeout 1d;` | 会话 ID（传统 Session Cache）在内存中保活 1 天 | 让同一用户 24 小时内重新建立连接时可复用主密钥，减少握手 CPU & RTT；时间过长会加大密钥被窃取后的利用窗口，一天属于兼顾性能与安全的折中。 |
| `ssl_session_cache shared:SSL:50m;` | 建一个 50 MiB 的共享内存区跨 worker 进程保存会话 | 大约能容纳 4–8 万条会话记录（取决于编译选项）；在多进程 nginx 中共享可显著提升复用率。 |
| `ssl_session_tickets off;` | **禁用**会话票据 (Session Ticket) | Session Ticket 会把主密钥加密后下发给客户端，若票据密钥轮换/保存不当，会削弱前向保密；许多现代浏览器都已支持更安全的 0-RTT/PSK 方案，所以小到中型站点直接关掉最省事。若你需要 Ticket(如高并发握手场景)，应配合自动轮换 `ssl_ticket_key`. |

### 安全头部配置

| 指令 | 功能 | 配置说明 |
|------|------|----------|
| `add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;` | **HSTS**，强制客户端未来 2 年（≈63072000 秒）只用 HTTPS 访问本域及全部子域，并宣告可加入浏览器 **preload list** | 避免中间人降级到 HTTP；注意：一旦上线并被 preload，未来无法轻易回退到 HTTP。 |
| `add_header X-Content-Type-Options "nosniff";` | 禁止浏览器 **MIME 嗅探** | 防止把纯文本/脚本误当成可执行资源，引发 XSS。 |
| `add_header X-Frame-Options "SAMEORIGIN";` | 仅允许本域把页面嵌入 `<iframe>` | 阻止点击劫持 (Clickjacking)。若需跨域嵌入可用 CSP 的 `frame-ancestors` 取代。 |
| `add_header Referrer-Policy "strict-origin-when-cross-origin";` | 控制 **Referer** 头 | 向同站点请求发送完整路径（便于统计），跨站点仅发送协议+域（保护隐私），兼顾可观测性与隐私。 |

## 高级配置选项

### HTTP/3 (QUIC) 支持

如果您的 Nginx 版本 >= 1.25，可以启用 HTTP/3 来提升性能：

```nginx
server {
    # 在 listen 443 ssl http2; 下方添加
    listen 443 quic reuseport;

    ssl_early_data on; # 允许TLS 1.3早期数据，减少握手延迟

    # 添加 Alt-Svc 头部，告知客户端 HTTP/3 可用
    add_header Alt-Svc 'h3=":443"; ma=86400';
}
```

**注意**: 启用 HTTP/3 需要 Nginx 编译时包含 `--with-http_v3_module`，并且服务器需要开放相应的 UDP 端口 (默认为 443/udp)。

### 高性能优化

```nginx
# 高性能场景的额外配置
ssl_reuse_buffers on;           # 重用 SSL 缓冲区
ssl_stapling on;                # 启用 OCSP Stapling
ssl_stapling_verify on;         # 验证 OCSP 响应
ssl_trusted_certificate /etc/letsencrypt/live/example.com/chain.pem;

# 减少 SSL 握手开销
ssl_session_cache shared:SSL:100m;  # 增大缓存
ssl_session_timeout 24h;            # 延长超时
```

## 不同场景的配置调整

### 兼容老旧客户端

如果必须支持非常老的客户端（如 Win XP、Java 6）：

```nginx
ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
ssl_ciphers 'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA';
```

**⚠️ 安全警告**: 建议单独起一个回退虚拟机或端口，而不是在主站点上降低安全标准。

### 高性能边缘节点

```nginx
# 开启 Session Ticket（需要密钥轮换）
ssl_session_tickets on;
ssl_ticket_key /etc/nginx/ssl/ticket.key;

# 0-RTT 支持（需谨慎使用）
ssl_early_data on;
proxy_set_header Early-Data $ssl_early_data;
```

### 企业级安全需求

```nginx
# 更严格的安全策略
ssl_protocols TLSv1.3;  # 仅 TLS 1.3
ssl_ciphers 'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256';

# 更严格的安全头部
add_header Content-Security-Policy "default-src 'none'; script-src 'self'; style-src 'self'; img-src 'self'; connect-src 'self'; font-src 'self'; object-src 'none'; media-src 'self'; frame-src 'none';" always;
add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
```

## 证书配置

### Let's Encrypt 证书路径

```nginx
server {
    # 标准证书路径（由 Certbot 管理）
    ssl_certificate     /etc/letsencrypt/live/example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
    
    # 可选：指定中间证书
    ssl_trusted_certificate /etc/letsencrypt/live/example.com/chain.pem;
}
```

### 通配符证书

使用通配符证书时，所有子域都可以使用同一证书：

```nginx
server {
    # 主域
    server_name example.com;
    ssl_certificate     /etc/letsencrypt/live/example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
}

server {
    # 任意子域
    server_name *.example.com;
    ssl_certificate     /etc/letsencrypt/live/example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
}
```

## 安全检测

### SSL 配置测试

```bash
# 使用 OpenSSL 测试 SSL 配置
openssl s_client -connect example.com:443 -servername example.com

# 测试特定 TLS 版本
openssl s_client -connect example.com:443 -tls1_2
openssl s_client -connect example.com:443 -tls1_3

# 检查证书信息
openssl x509 -in /etc/letsencrypt/live/example.com/cert.pem -text -noout
```

### 在线安全测试

推荐使用以下在线工具检测 SSL 配置：

1. **SSL Labs SSL Test**: https://www.ssllabs.com/ssltest/
2. **Mozilla Observatory**: https://observatory.mozilla.org/
3. **SecurityHeaders.com**: https://securityheaders.com/

## 性能监控

### SSL 性能指标

```nginx
# 启用 SSL 相关日志变量
log_format ssl_log '$remote_addr - $remote_user [$time_local] '
                   '"$request" $status $body_bytes_sent '
                   '"$http_referer" "$http_user_agent" '
                   'ssl_protocol=$ssl_protocol ssl_cipher=$ssl_cipher '
                   'ssl_session_reused=$ssl_session_reused';

server {
    access_log /var/log/nginx/ssl.log ssl_log;
}
```

### 性能优化验证

```bash
# 检查 SSL 握手时间
curl -w "@curl-format.txt" -o /dev/null -s "https://example.com/"

# curl-format.txt 内容：
# time_namelookup:  %{time_namelookup}\n
# time_connect:     %{time_connect}\n
# time_appconnect:  %{time_appconnect}\n
# time_pretransfer: %{time_pretransfer}\n
```

## 配置总结

SSL 安全配置的核心要点：

✅ **协议安全**: 仅使用 TLS 1.2/1.3，淘汰不安全的旧版本
✅ **密码套件**: 选择现代、安全的加密算法
✅ **会话管理**: 平衡性能与安全的会话复用策略
✅ **安全头部**: 全面的浏览器安全策略
✅ **证书管理**: 自动化的证书申请和续期
✅ **性能优化**: 减少握手开销，提升用户体验

这套配置代表了 **现代 HTTPS 安全基线**，在保证兼容性的同时提供了最佳的安全防护。 