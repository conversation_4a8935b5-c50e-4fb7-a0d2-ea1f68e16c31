# 静态站点配置详解

本文档详细介绍 Nginx 静态站点托管的配置方法和各项参数说明。

## 完整配置示例

```nginx
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name <domain.com>;

    root /var/www/<site-directory>;

    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/<domain.com>/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/<domain.com>/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数

    # ACME Challenge 路径
    # Let's Encrypt (Certbot) 用这个路径通过 HTTPS 验证域名所有权
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;    # 这个路径要和 docker-compose.yml 里 certbot 卷挂载的路径一致
        default_type "text/plain";
        allow all;                # 必须允许所有访问，LCS 服务器才能进来
    }

    # 高并发优化选项
    gzip_static on; # 前提是静态文件已经预先用 gzip 压缩过了，并带 .gz 后缀
    open_file_cache max=10000 inactive=30s;
    sendfile on;
    tcp_nopush on;

    # 统一资源缓存策略
    include snippets/static-cache.conf; # 具体的缓存规则在这个文件里定义

    # SPA (单页应用) 路由回退
    # try_files $uri $uri/ /index.html;

    # 日志路径
    access_log /var/log/nginx/<domain.com>.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/<domain.com>.error.log warn;
}

# HTTP 强制跳转 HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name <domain.com>;

    # ACME Challenge 路径
    # Let's Encrypt 进行 HTTP-01 验证时需要这个路径
    # 关键：这个 location 块必须放在下面的 return 301 前面，不然验证请求会被重定向，导致失败
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;    # 和 HTTPS server 块里的路径保持一致
        default_type "text/plain";
        allow all;
    }

    # 其他所有 HTTP 请求，一律 301 重定向到 HTTPS
    return 301 https://$host$request_uri;
}
```

## 配置参数详解

### 基本配置

**部署时占位符替换**：
* `<domain.com>`: 换成真实域名
* `<site-directory>`: 换成站点文件实际的根目录

### SSL 配置

```nginx
server {
    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/<domain.com>/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/<domain.com>/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数
}
```

- `ssl_certificate` 和 `ssl_certificate_key` 分别指向 SSL 证书和私钥的路径
- 实际的文件内容由 Certbot 进行管理，我们只需要指定路径即可
- `include snippets/ssl-params.conf` 引入通用 SSL 安全参数配置

### ACME Challenge 配置

```nginx
# HTTPS server 块中
location ^~ /.well-known/acme-challenge/ {
    root /var/www/certbot;
    default_type "text/plain";
    allow all;
}

# HTTP server 块中
location ^~ /.well-known/acme-challenge/ {
    root /var/www/certbot;
    default_type "text/plain";
    allow all;
}
```

**重要说明**：
- ACME Challenge 用于 Let's Encrypt 的域名验证
- 必须在 HTTP 和 HTTPS server 块中都配置此路径
- HTTP server 块中这个 location 必须放在 `return 301` 前面，否则验证请求会被重定向导致失败
- `root` 路径必须与 docker-compose.yml 中 certbot 卷挂载路径一致

### 性能优化配置

```nginx
server {
    # 高并发优化选项
    gzip_static on;
    open_file_cache max=10000 inactive=30s;
    sendfile on;
    tcp_nopush on;
}
```

这些优化配置的详细说明请参考 [📖 性能优化配置详解](./performance-optimization.md)。

### 缓存策略

```nginx
server {
    # 统一资源缓存策略
    include snippets/static-cache.conf;
}
```

通过引入外部文件来统一管理各类静态资源的浏览器缓存策略。详细的缓存配置请参考 [📖 性能优化配置详解](./performance-optimization.md)。

### SPA 应用支持

```nginx
server {
    # SPA (单页应用) 路由回退
    # try_files $uri $uri/ /index.html;
}
```

**使用场景**：
- **默认模式** (`try_files $uri $uri/ =404`): 适用于传统多页面应用，找不到文件直接返回 404
- **SPA 模式** (`try_files $uri $uri/ /index.html`): 适用于单页应用，未匹配的路径返回 index.html 由前端路由处理

### 日志配置

```nginx
server {
    # 日志路径
    access_log /var/log/nginx/<domain.com>.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/<domain.com>.error.log warn;
}
```

**配置说明**：
- `access_log`: 访问日志，使用 `main` 格式，带 32KB 缓冲，5秒刷新
- `error_log`: 错误日志，记录 `warn` 级别及以上的错误信息

## 不同场景的配置变体

### 博客站点

```nginx
server {
    listen 443 ssl http2;
    server_name blog.example.com;
    root /var/www/blog;
    
    include snippets/ssl-params.conf;
    include snippets/static-cache.conf;
    
    # 博客通常更新较频繁，可以缩短 HTML 缓存时间
    location ~* \.html$ {
        expires 1h;
        etag on;
    }
    
    # 优化图片缓存
    location ~* \.(jpg|jpeg|png|gif|webp|svg)$ {
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

### SPA 应用

```nginx
server {
    listen 443 ssl http2;
    server_name app.example.com;
    root /var/www/spa;
    
    include snippets/ssl-params.conf;
    
    # SPA 路由回退
    location / {
        try_files $uri $uri/ /index.html;
        
        # 防止 index.html 被缓存
        location = /index.html {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # 静态资源强缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 文档站点

```nginx
server {
    listen 443 ssl http2;
    server_name docs.example.com;
    root /var/www/docs;
    
    include snippets/ssl-params.conf;
    
    # 启用目录浏览（如果需要）
    autoindex on;
    autoindex_exact_size off;
    autoindex_localtime on;
    
    # 文档通常较稳定，可以较长缓存
    location ~* \.(html|htm)$ {
        expires 1d;
        etag on;
    }
    
    # PDF 等文档文件
    location ~* \.(pdf|doc|docx|ppt|pptx|xls|xlsx)$ {
        expires 7d;
        add_header Content-Disposition "inline";
    }
}
```

## 安全加固

### 基础安全配置

```nginx
server {
    # 隐藏 Nginx 版本信息
    server_tokens off;
    
    # 限制请求方法
    if ($request_method !~ ^(GET|HEAD|POST)$ ) {
        return 405;
    }
    
    # 防止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 防止访问备份文件
    location ~* \.(bak|backup|old|orig|tmp)$ {
        deny all;
    }
}
```

### 访问控制

```nginx
server {
    # IP 白名单（管理后台等）
    location /admin/ {
        allow ***********/24;
        allow 10.0.0.0/8;
        deny all;
        
        try_files $uri $uri/ /admin/index.html;
    }
    
    # 限制访问频率
    location /api/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://backend;
    }
}
```

## 性能监控

### 状态监控

```nginx
server {
    # Nginx 状态页面（仅内网访问）
    location /nginx_status {
        stub_status on;
        allow 127.0.0.1;
        allow ***********/16;
        deny all;
    }
}
```

### 自定义日志格式

```nginx
# 在 http 块中定义
log_format combined_with_time '$remote_addr - $remote_user [$time_local] '
                              '"$request" $status $body_bytes_sent '
                              '"$http_referer" "$http_user_agent" '
                              '$request_time';

server {
    access_log /var/log/nginx/detailed.log combined_with_time;
}
```

## 故障排查

### 常见问题

1. **404 错误**
   - 检查 `root` 路径是否正确
   - 确认文件权限 (`nginx` 用户需要读取权限)
   - 验证 `index` 指令配置

2. **ACME Challenge 失败**
   - 确认 HTTP server 块中 `/.well-known/acme-challenge/` location 在 `return 301` 之前
   - 检查 `/var/www/certbot` 目录权限
   - 验证防火墙是否阻止了 80 端口

3. **缓存问题**
   - 使用浏览器开发者工具检查缓存头
   - 临时禁用缓存测试：`expires -1;`
   - 清除浏览器缓存重新测试

### 调试命令

```bash
# 测试配置语法
nginx -t

# 重新加载配置
nginx -s reload

# 查看错误日志
tail -f /var/log/nginx/error.log

# 测试 SSL 配置
openssl s_client -connect example.com:443 -servername example.com

# 检查文件权限
ls -la /var/www/
```

## 配置模板

### 基础静态站点模板

创建文件 `sites-available/static-template.conf`：

```nginx
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name DOMAIN_NAME;

    root /var/www/SITE_DIRECTORY;
    index index.html index.htm;

    # SSL 配置
    ssl_certificate     /etc/letsencrypt/live/DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/DOMAIN_NAME/privkey.pem;
    include snippets/ssl-params.conf;

    # ACME Challenge
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    # 性能优化
    gzip_static on;
    sendfile on;
    tcp_nopush on;

    # 缓存策略
    include snippets/static-cache.conf;

    # 日志
    access_log /var/log/nginx/DOMAIN_NAME.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/DOMAIN_NAME.error.log warn;
}

server {
    listen 80;
    listen [::]:80;
    server_name DOMAIN_NAME;

    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    return 301 https://$host$request_uri;
}
```

### 使用模板

```bash
# 复制模板
cp sites-available/static-template.conf sites-available/myblog.conf

# 替换占位符
sed -i 's/DOMAIN_NAME/myblog.example.com/g' sites-available/myblog.conf
sed -i 's/SITE_DIRECTORY/myblog/g' sites-available/myblog.conf

# 启用站点
ln -s ../sites-available/myblog.conf sites-enabled/

# 测试并重载
nginx -t && nginx -s reload
```

## 总结

静态站点配置的核心要点：

✅ **HTTPS 优先**: 强制 HTTP 跳转，配置安全的 SSL 参数
✅ **证书自动化**: 支持 Let's Encrypt 自动申请和续期
✅ **性能优化**: 启用 gzip、sendfile、文件缓存等优化
✅ **合理缓存**: 基于文件类型设置不同的缓存策略
✅ **日志监控**: 配置详细的访问和错误日志
✅ **安全防护**: 隐藏服务器信息，限制访问方法
✅ **灵活配置**: 支持 SPA、博客、文档等不同场景

这套配置提供了生产级别的静态站点托管能力，既保证了安全性，又优化了性能。 