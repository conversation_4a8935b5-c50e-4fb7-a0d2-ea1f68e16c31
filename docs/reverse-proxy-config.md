# 反向代理配置详解

本文档详细介绍 Nginx 反向代理 Docker 服务的配置方法和各项参数说明。

## 完整配置示例

```nginx
# 上游服务器定义
upstream nextcloud { server 127.0.0.1:9000; }
upstream gitea { server 127.0.0.1:9001; }

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name <domain.com>;

    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/<domain.com>/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/<domain.com>/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数

    # ACME Challenge 路径 (HTTPS 验证)
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    # 日志路径
    access_log /var/log/nginx/<domain.com>.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/<domain.com>.error.log warn;

    # Nextcloud（仅内网访问）
    location ^~ /nextcloud/ {
        # IP访问控制
        allow 10.0.0.0/8;
        allow ***********/16;
        deny all;

        # 反向代理配置
        proxy_pass http://nextcloud/;  # 注意尾部的斜杠，它会移除匹配到的 /nextcloud/ 部分
        include snippets/proxy-common.conf;
        include snippets/proxy-nextcloud.conf;  # Nextcloud 专用配置
    }

    # Gitea（公网访问）
    location ^~ /gitea/ {
        proxy_pass http://gitea/; # 注意尾部的斜杠
        include snippets/proxy-common.conf;
        include snippets/proxy-security-moderate.conf;  # 公开服务安全配置

        # WebSocket支持 (Gitea 可能需要)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location / { return 404; } # 其他未匹配路径返回 404
}

# HTTP 强制跳转 HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name <domain.com>;

    # ACME Challenge 路径 (HTTP-01 验证)
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    # 其他所有 HTTP 请求，一律 301 重定向到 HTTPS
    return 301 https://$host$request_uri;
}
```

## 配置参数详解

### Upstream 服务器定义

```nginx
upstream nextcloud { server 127.0.0.1:9000; }
upstream gitea { server 127.0.0.1:9001; }
```

**基本说明**：
- `upstream` 块定义一组后端服务器
- 可在 `proxy_pass` 中通过名称引用：`proxy_pass http://nextcloud/`
- 支持负载均衡和健康检查

#### 基础配置

```nginx
# 单个后端服务器
upstream basic_backend {
    server 127.0.0.1:8080;
}

# 多个后端服务器（负载均衡）
upstream load_balanced_backend {
    server ************:8080 weight=3;        # 权重为3，处理更多请求
    server ************:8080 weight=1;        # 权重为1，处理较少请求
    server ************:8080 backup;          # 备用服务器
    server ************:8080 down;            # 临时标记为不可用
}
```

#### 高可用配置

```nginx
upstream high_availability_backend {
    server **********:9000 max_fails=3 fail_timeout=30s;
    server **********:9000 max_fails=3 fail_timeout=30s;
    server **********:9000 backup max_fails=2 fail_timeout=60s;

    # 负载均衡算法
    least_conn;  # 最少连接算法，也可以是 ip_hash 或默认的轮询
}
```

#### Server 参数详解

| 参数 | 说明 | 示例 | 默认值 |
|------|------|------|--------|
| **`weight=number`** | 服务器权重，数值越大分配的请求越多 | `weight=3` | `1` |
| **`max_fails=number`** | 允许的最大失败次数，超过后标记为不可用 | `max_fails=3` | `1` |
| **`fail_timeout=time`** | 失败超时时间，服务器被标记不可用的持续时间 | `fail_timeout=30s` | `10s` |
| **`backup`** | 标记为备用服务器，仅在主服务器全部不可用时启用 | `backup` | - |
| **`down`** | 永久标记服务器为不可用状态 | `down` | - |
| **`max_conns=number`** | 限制到该服务器的最大并发连接数 | `max_conns=100` | `0`(无限制) |
| **`resolve`** | 监控服务器域名的DNS变化并自动更新IP | `resolve` | - |

### Location 块配置

#### 路径匹配

```nginx
# 精确匹配
location = /api {
    proxy_pass http://backend;
}

# 前缀匹配（优先级高）
location ^~ /admin/ {
    proxy_pass http://backend;
}

# 正则匹配
location ~ ^/api/v[0-9]+/ {
    proxy_pass http://backend;
}

# 普通前缀匹配
location /app/ {
    proxy_pass http://backend;
}
```

#### 代理传递配置

```nginx
location /app/ {
    # 基础代理
    proxy_pass http://backend/;  # 注意尾部斜杠的影响
    
    # 引入通用配置
    include snippets/proxy-common.conf;
    include snippets/proxy-security-moderate.conf;
}
```

**代理路径处理**：

| proxy_pass 配置 | 请求 URL | 传递给后端的 URL |
|-----------------|----------|------------------|
| `proxy_pass http://backend;` | `/app/test` | `/app/test` |
| `proxy_pass http://backend/;` | `/app/test` | `/test` |
| `proxy_pass http://backend/api;` | `/app/test` | `/apitest` |
| `proxy_pass http://backend/api/;` | `/app/test` | `/api/test` |

### 访问控制

#### IP 访问控制

```nginx
location /admin/ {
    # 允许内网访问
    allow 10.0.0.0/8;
    allow ***********/16;
    allow **********/12;
    
    # 允许特定 IP
    allow ***********;
    
    # 拒绝其他所有访问
    deny all;
    
    proxy_pass http://backend;
}
```

#### 基于用户认证

```nginx
location /secure/ {
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    proxy_pass http://backend;
}
```

#### 基于时间的访问控制

```nginx
location /maintenance/ {
    # 仅在维护时间段开放
    access_by_lua_block {
        local hour = tonumber(os.date("%H"))
        if hour < 2 or hour > 4 then
            ngx.status = 503
            ngx.say("Maintenance window: 02:00-04:00")
            ngx.exit(503)
        end
    }
    
    proxy_pass http://backend;
}
```

### WebSocket 支持

```nginx
location /websocket/ {
    proxy_pass http://backend;
    
    # WebSocket 升级
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    
    # 长连接配置
    proxy_read_timeout 86400;
    proxy_send_timeout 86400;
    
    include snippets/proxy-common.conf;
}
```

### 大文件上传

```nginx
location /upload/ {
    client_max_body_size 100M;  # 最大文件大小
    client_body_timeout 300s;   # 上传超时
    
    # 禁用代理缓冲以支持大文件
    proxy_request_buffering off;
    proxy_max_temp_file_size 0;
    
    proxy_pass http://backend;
    include snippets/proxy-common.conf;
}
```

## 通用配置片段

### proxy-common.conf

```nginx
# ========== 基本代理头部 ==========
proxy_set_header Host              $host;
proxy_set_header X-Real-IP         $remote_addr;
proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host  $host;
proxy_set_header X-Forwarded-Port  $server_port;

# ========== 连接优化 ==========
proxy_http_version    1.1;       # 启用 HTTP/1.1，支持 Keep-Alive
proxy_set_header      Connection "";  # 启用连接复用

# ========== 缓冲配置 ==========
proxy_buffering       on;        # 开启代理缓冲，减轻后端压力
proxy_buffer_size     8k;        # 响应头缓冲区大小
proxy_buffers         8 32k;     # 响应体缓冲区数量和大小
proxy_busy_buffers_size 16k;     # 忙碌缓冲区大小

# ========== 超时设置 ==========
proxy_connect_timeout 60s;       # 后端连接超时时间
proxy_read_timeout    300s;      # 后端读取响应超时时间
proxy_send_timeout    60s;       # 向后端发送请求超时时间

# ========== 重试配置 ==========
proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
proxy_next_upstream_tries 3;
proxy_next_upstream_timeout 10s;
```

### proxy-security-moderate.conf

```nginx
# ========== 安全头部 ==========
add_header X-Content-Type-Options            "nosniff" always;
add_header X-Frame-Options                   "SAMEORIGIN" always;
add_header X-XSS-Protection                  "1; mode=block" always;
add_header Referrer-Policy                   "strict-origin-when-cross-origin" always;

# ========== 信息隐藏 ==========
proxy_hide_header X-Powered-By;  # 隐藏后端技术信息
proxy_hide_header Server;        # 隐藏服务器版本信息

# ========== 请求限制 ==========
# 限制请求频率（需要在 http 块中定义 limit_req_zone）
limit_req zone=general burst=20 nodelay;

# 限制连接数
limit_conn addr 10;
```

### proxy-security-strict.conf

```nginx
# ========== 严格安全头部 ==========
add_header X-Content-Type-Options            "nosniff" always;
add_header X-Frame-Options                   "DENY" always;
add_header X-XSS-Protection                  "1; mode=block" always;
add_header Referrer-Policy                   "no-referrer" always;
add_header Content-Security-Policy           "default-src 'self'" always;

# ========== 严格访问控制 ==========
# 仅允许内网访问
allow 10.0.0.0/8;
allow ***********/16;
allow **********/12;
deny all;

# ========== 信息隐藏 ==========
proxy_hide_header X-Powered-By;
proxy_hide_header Server;
proxy_hide_header X-AspNet-Version;
proxy_hide_header X-AspNetMvc-Version;

# ========== 严格限制 ==========
limit_req zone=strict burst=5 nodelay;
limit_conn addr 3;
```

## 不同应用的配置示例

### Nextcloud 配置

```nginx
location ^~ /nextcloud/ {
    # 访问控制
    allow ***********/16;
    deny all;
    
    proxy_pass http://nextcloud/;
    include snippets/proxy-common.conf;
    
    # Nextcloud 特殊配置
    proxy_set_header X-Forwarded-Ssl on;
    client_max_body_size 10G;
    client_body_timeout 300s;
    
    # WebDAV 支持
    proxy_request_buffering off;
    proxy_max_temp_file_size 0;
    
    # 特殊头部
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Forwarded-Ssl on;
}
```

### Gitea 配置

```nginx
location ^~ /gitea/ {
    proxy_pass http://gitea/;
    include snippets/proxy-common.conf;
    include snippets/proxy-security-moderate.conf;
    
    # Git LFS 支持
    client_max_body_size 1G;
    
    # WebSocket 支持（SSH over WebSocket）
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    
    # 长连接支持
    proxy_read_timeout 300s;
    proxy_send_timeout 300s;
}
```

### API 网关配置

```nginx
# API 版本路由
location ~ ^/api/v([0-9]+)/(.*) {
    set $version $1;
    set $path $2;
    
    # 根据版本路由到不同后端
    if ($version = "1") {
        proxy_pass http://api_v1/$path;
    }
    if ($version = "2") {
        proxy_pass http://api_v2/$path;
    }
    
    include snippets/proxy-common.conf;
    
    # API 特殊配置
    proxy_set_header Accept-Encoding "";
    add_header X-API-Version $version;
}
```

### 微服务网关

```nginx
# 认证服务
location /auth/ {
    proxy_pass http://auth-service/;
    include snippets/proxy-common.conf;
}

# 用户服务
location /users/ {
    # 认证检查
    auth_request /auth/validate;
    
    proxy_pass http://user-service/;
    include snippets/proxy-common.conf;
}

# 文件服务
location /files/ {
    auth_request /auth/validate;
    
    proxy_pass http://file-service/;
    include snippets/proxy-common.conf;
    
    # 文件上传配置
    client_max_body_size 500M;
    proxy_request_buffering off;
}
```

## 健康检查和监控

### 被动健康检查

```nginx
upstream backend {
    server ************:8080 max_fails=3 fail_timeout=30s;
    server ************:8080 max_fails=3 fail_timeout=30s;
}

location / {
    proxy_pass http://backend;
    
    # 错误时尝试下一个服务器
    proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
    proxy_next_upstream_tries 2;
    proxy_next_upstream_timeout 5s;
}
```

### 自定义健康检查页面

```nginx
location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
}

# 后端健康检查
location /backend/health {
    internal;  # 仅内部访问
    proxy_pass http://backend/health;
    proxy_pass_request_body off;
    proxy_set_header Content-Length "";
    proxy_set_header X-Original-URI $request_uri;
}
```

### 监控配置

```nginx
# 状态监控（仅内网）
location /nginx_status {
    stub_status on;
    allow 127.0.0.1;
    allow ***********/16;
    deny all;
    access_log off;
}

# 上游服务器状态
location /upstream_status {
    upstream_show;
    allow 127.0.0.1;
    allow ***********/16;
    deny all;
    access_log off;
}
```

## 性能优化

### 连接池优化

```nginx
upstream backend {
    server ************:8080;
    server ************:8080;
    
    # 连接保持
    keepalive 32;
    keepalive_requests 1000;
    keepalive_timeout 60s;
}

location / {
    proxy_pass http://backend;
    
    # 连接复用
    proxy_http_version 1.1;
    proxy_set_header Connection "";
}
```

### 缓存配置

```nginx
# 代理缓存路径
proxy_cache_path /var/cache/nginx/proxy 
                levels=1:2 
                keys_zone=proxy_cache:10m 
                max_size=1g 
                inactive=60m;

location /api/ {
    proxy_pass http://backend;
    
    # 缓存配置
    proxy_cache proxy_cache;
    proxy_cache_valid 200 302 10m;
    proxy_cache_valid 404 1m;
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
    
    # 缓存键
    proxy_cache_key "$scheme$request_method$host$request_uri";
    
    # 缓存头部
    add_header X-Cache-Status $upstream_cache_status;
}
```

## 故障排查

### 常见问题

1. **502 Bad Gateway**
   - 检查后端服务是否运行
   - 验证 upstream 配置的 IP 和端口
   - 检查防火墙设置

2. **504 Gateway Timeout**
   - 增加 proxy_read_timeout
   - 检查后端服务响应时间
   - 优化后端性能

3. **413 Request Entity Too Large**
   - 增加 client_max_body_size
   - 检查后端服务的文件大小限制

### 调试配置

```nginx
# 详细错误日志
error_log /var/log/nginx/debug.log debug;

# 调试特定 location
location /debug/ {
    # 详细日志
    access_log /var/log/nginx/debug.access.log;
    error_log /var/log/nginx/debug.error.log debug;
    
    # 显示代理信息
    add_header X-Debug-Backend $upstream_addr;
    add_header X-Debug-Response-Time $upstream_response_time;
    add_header X-Debug-Status $upstream_status;
    
    proxy_pass http://backend;
}
```

### 性能分析

```bash
# 查看连接状态
ss -tuln | grep :80

# 监控 Nginx 进程
top -p $(pgrep nginx)

# 查看访问日志统计
tail -f /var/log/nginx/access.log | grep -E "(502|504|timeout)"

# 测试后端连接
curl -I http://127.0.0.1:8080/health

# 检查 upstream 状态
curl http://127.0.0.1/nginx_status
```

## 配置模板

### 反向代理模板

```nginx
# 上游服务器
upstream SERVICE_NAME {
    server 127.0.0.1:SERVICE_PORT;
    keepalive 8;
}

server {
    listen 443 ssl http2;
    server_name DOMAIN_NAME;
    
    # SSL 配置
    ssl_certificate     /etc/letsencrypt/live/DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/DOMAIN_NAME/privkey.pem;
    include snippets/ssl-params.conf;
    
    # 代理配置
    location /SERVICE_PATH/ {
        proxy_pass http://SERVICE_NAME/;
        include snippets/proxy-common.conf;
        include snippets/proxy-security-moderate.conf;
        
        # 根据需要添加特殊配置
        # client_max_body_size 100M;
        # proxy_read_timeout 300s;
    }
    
    # 日志
    access_log /var/log/nginx/DOMAIN_NAME.access.log main;
    error_log  /var/log/nginx/DOMAIN_NAME.error.log warn;
}
```

## 总结

反向代理配置的核心要点：

✅ **服务发现**: 通过 upstream 块管理后端服务
✅ **负载均衡**: 支持多种算法和健康检查
✅ **路径处理**: 灵活的 URL 重写和路径匹配
✅ **访问控制**: IP 限制、认证和授权
✅ **性能优化**: 连接复用、缓冲和缓存
✅ **容错机制**: 重试、故障转移和降级
✅ **安全防护**: 头部过滤和安全策略
✅ **监控诊断**: 状态检查和日志分析

这套配置提供了企业级的反向代理能力，能够支撑复杂的微服务架构和高并发场景。 