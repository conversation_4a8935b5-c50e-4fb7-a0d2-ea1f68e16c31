# 上游服务器定义
upstream nextcloud { server 127.0.0.1:4433; } # 假设 Nextcloud 服务在本机的 9000 端口

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name service.yinshe.online;

    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/service.yinshe.online/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/service.yinshe.online/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数

    # ACME Challenge 路径 (HTTPS 验证)
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    # 日志路径
    access_log /var/log/nginx/service.yinshe.online.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/service.yinshe.online.error.log warn;

    location ^~ /nextcloud/ {

        proxy_pass https://nextcloud/;  # 注意尾部的斜杠，它会移除匹配到的 /nextcloud/ 部分
        include snippets/proxy-common.conf;

        # 大文件上传优化
        client_max_body_size 20G;
        proxy_read_timeout 3600; # 增加读取超时时间
        proxy_send_timeout 3600; # 增加发送超时时间

        # WebDAV支持 (可选，如果Nextcloud启用了WebDAV)
        proxy_request_buffering off; # 对于大文件上传和 WebDAV，建议关闭请求缓冲
    }
}

# HTTP 强制跳转 HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name service.yinshe.online;

    # ACME Challenge 路径 (HTTP-01 验证)
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    # 其他所有 HTTP 请求，一律 301 重定向到 HTTPS
    return 301 https://$host$request_uri;
}