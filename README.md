# Nginx 最佳实践与 Docker-化部署手册

## 前言

初次接触 nginx 时，对其繁杂的配置感到十分头疼。遂想整理一个适用于个人的最佳实践，于是便有了这篇文档。

> 适用场景
> * 多子域静态站点（博客 / 文档 / SPA 等）
> * 多容器业务（Nextcloud、Gitea…）共用一台服务器，通过子域或路径反向代理
> * 全自动申请与续签 Let's Encrypt 通配符证书
> * 基于 Docker Compose 的一键部署

---

## 目录

TODO: 文档目录

---

## 部署流程

整个部署流程可以大致分成以下几个部分：

TODO: 部署流程图

下面，我来详细说明各流程细节。

### 1. 创建目录结构

部署 nginx 的第一步是创建对应的目录结构。

我在参考 [Debian/Ubuntu 打包团队的惯例](https://www.kevinhooke.com/2025/01/02/configuring-nginx-virtual-hosts-with-sites-available-sites-enabled/) 和在 docker 中部署 nginx + certbot 的[一篇博客](https://dev.to/marrouchi/the-challenge-about-ssl-in-docker-containers-no-one-talks-about-32gh)后，最终得到的目录结构如下：

```
project-root/
├── docker-compose.yml      # Docker Compose 配置
├── cloudflare.ini          # Cloudflare API Token（权限 600）
├── .env                    # 环境变量配置
├── scripts/                # 自动化脚本
│   ├── 00_start.sh         # 容器启动脚本（PID 1）
│   ├── 10_init_cert.sh     # 首次证书申请脚本
│   └── 20_renew_loop.sh    # 证书续期循环脚本
├── nginx/                  # Nginx 配置目录
│   ├── sites-available/    # 所有站点配置
│   ├── sites-enabled/      # 软链指向启用站点
│   ├── snippets/           # 公共片段
│   │   ├── ssl-params.conf           # SSL 安全配置片段
│   │   ├── proxy-common.conf         # 反向代理通用配置片段
│   │   ├── proxy-security-strict.conf     # 私有服务严格安全配置
│   │   ├── proxy-security-moderate.conf   # 公开服务适度安全配置
│   │   ├── proxy-nextcloud.conf           # Nextcloud 专用配置
│   │   └── static-cache.conf              # 静态资源缓存配置
│   └── www/                # 静态文件目录
└── certbot/                # 证书存储（Docker 卷挂载）
    ├── conf/               # /etc/letsencrypt
    └── log/                # /var/log/letsencrypt
```

这些目录和脚本的作用我会在部署过程中逐步介绍。现在，我们先创建这些目录和文件：

```bash
# 创建项目目录结构
mkdir -p {scripts,nginx/{sites-available,sites-enabled,snippets,www},certbot/{conf,log}}

# 创建配置文件模板
touch nginx/snippets/{ssl-params.conf,proxy-common.conf,proxy-security-strict.conf,proxy-security-moderate.conf,proxy-nextcloud.conf,static-cache.conf}

# 设置脚本权限
chmod +x scripts/*.sh

# 设置 Cloudflare 配置文件权限
chmod 600 cloudflare.ini
```

### 2. 站点配置

目录结构创建完毕后就可以开始编辑具体的站点配置了。

在 `sites-available/` 目录下创建站点配置文件。当站点配置编辑完毕后，就可以软链接到 `sites-enabled/` 目录下，表示配置已启用。而 nginx.conf 中仅包含 `sites-enabled/*` 的配置，这样就能保证只有配置好的文件才会被 `nginx` 解析，同时后续如果想要下线某站点，只需要删除软链接即可。

```
nginx/
└── conf/
    ├── sites-available/        # 所有站点配置
        ├── site1.conf
        ├── site2.conf
        └── ...
    └── sites-enabled/          # 软链指向启用站点
        ├── site1.conf          # 软链到 sites-available/site1.conf，表示已启用的配置
        └── ...
```

根据不同的使用需求，我主要会用到两种配置类型：静态站点托管和反向代理。接下来我会详细说明这两种配置的具体实现方法。

#### 静态站点托管配置

静态站点托管主要用于部署博客、文档站点、SPA 应用等。以下是一个完整的静态站点配置示例：

```nginx
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name blog.example.com;

    root /var/www/blog;

    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/blog.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/blog.example.com/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数

    # ACME Challenge 路径
    # Let's Encrypt (Certbot) 用这个路径通过 HTTPS 验证域名所有权
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;    # 这个路径要和 docker-compose.yml 里 certbot 卷挂载的路径一致
        default_type "text/plain";
        allow all;                # 必须允许所有访问，Let's Encrypt 服务器才能进来
    }

    # 高并发优化选项
    gzip_static on; # 前提是静态文件已经预先用 gzip 压缩过了，并带 .gz 后缀
    open_file_cache max=10000 inactive=30s;
    sendfile on;
    tcp_nopush on;

    # 统一资源缓存策略
    include snippets/static-cache.conf; # 具体的缓存规则在这个文件里定义

    # SPA (单页应用) 路由回退 - 如果是 SPA 应用需要启用此行
    # try_files $uri $uri/ /index.html;

    # 传统多页面应用的文件查找策略
    try_files $uri $uri/ =404;

    # 日志路径
    access_log /var/log/nginx/blog.example.com.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/blog.example.com.error.log warn;
}

# HTTP 强制跳转 HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name blog.example.com;

    # ACME Challenge 路径
    # Let's Encrypt 进行 HTTP-01 验证时需要这个路径
    # 关键：这个 location 块必须放在下面的 return 301 前面，不然验证请求会被重定向，导致失败
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;    # 和 HTTPS server 块里的路径保持一致
        default_type "text/plain";
        allow all;
    }

    # 其他所有 HTTP 请求，一律 301 重定向到 HTTPS
    return 301 https://$host$request_uri;
}
```

**配置关键点说明**：

1. **SSL 证书路径**：由 Certbot 自动管理，只需正确指定路径即可
2. **ACME Challenge**：用于 Let's Encrypt 域名验证，HTTP 和 HTTPS 都需要配置
3. **性能优化**：包含了 gzip、文件缓存等优化选项
4. **SPA 支持**：通过 `try_files` 指令处理前端路由

#### 反向代理配置

反向代理主要用于将请求转发到后端的 Docker 服务。这种配置适合部署 Nextcloud、Gitea 等容器化应用：

```nginx
# 上游服务器定义
upstream nextcloud { server 127.0.0.1:9000; }
upstream gitea { server 127.0.0.1:9001; }

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name services.example.com;

    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/services.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/services.example.com/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数

    # ACME Challenge 路径 (HTTPS 验证)
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    # 日志路径
    access_log /var/log/nginx/services.example.com.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/services.example.com.error.log warn;

    # Nextcloud（仅内网访问）
    location ^~ /nextcloud/ {
        # IP访问控制
        allow 10.0.0.0/8;
        allow ***********/16;
        deny all;

        # 反向代理配置
        proxy_pass http://nextcloud/;  # 注意尾部的斜杠，它会移除匹配到的 /nextcloud/ 部分
        include snippets/proxy-common.conf;
        include snippets/proxy-nextcloud.conf;  # Nextcloud 专用配置
    }

    # Gitea（公网访问）
    location ^~ /gitea/ {
        proxy_pass http://gitea/; # 注意尾部的斜杠
        include snippets/proxy-common.conf;
        include snippets/proxy-security-moderate.conf;  # 公开服务安全配置

        # WebSocket支持 (Gitea 可能需要)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location / { return 404; } # 其他未匹配路径返回 404
}

# HTTP 强制跳转 HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name services.example.com;

    # ACME Challenge 路径 (HTTP-01 验证)
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    # 其他所有 HTTP 请求，一律 301 重定向到 HTTPS
    return 301 https://$host$request_uri;
}
```

**反向代理关键点说明**：

1. **Upstream 定义**：集中管理后端服务器地址，支持负载均衡
2. **路径处理**：`proxy_pass` 尾部斜杠会影响路径重写
3. **访问控制**：通过 `allow/deny` 指令限制访问来源
4. **WebSocket 支持**：某些应用需要额外的 WebSocket 配置

站点配置完成后，我们需要通过软链接来启用这些配置：

```bash
# 启用静态站点
ln -s /etc/nginx/sites-available/blog.conf /etc/nginx/sites-enabled/

# 启用反向代理站点
ln -s /etc/nginx/sites-available/services.conf /etc/nginx/sites-enabled/

# 检查配置语法
nginx -t
```

接下来我们需要配置 Docker 部署环境，以实现 SSL 证书的自动管理和 Nginx 的容器化运行。

### 3. Docker 部署

为了实现完全自动化的 SSL 证书获取和续签，我们采用以下方案：

- **Certbot 容器先启动**：负责首次证书申请和后续自动续签
- **Nginx 容器依赖 Certbot**：确保证书就绪后才启动
- **通配符证书**：支持 `*.example.com` 和 `example.com`，新增子域无需修改配置
- **DNS-01 验证**：使用 Cloudflare DNS API，无需 HTTP 验证路径

#### 目录结构

```
project-root/
├─ docker-compose.yml
├─ cloudflare.ini          # CF_TOKEN，chmod 600
├─ .env                    # CERTBOT_EMAIL、CERTBOT_DOMAINS 等
├─ scripts/
│   ├─ 00_start.sh         # entrypoint，PID 1
│   ├─ 10_init_cert.sh     # 首次签发并阻塞
│   └─ 20_renew_loop.sh    # 12 h 续期 + reload
└─ nginx/
    ├─ sites-available/
    ├─ sites-enabled/
    ├─ snippets/
    └─ www/
```

#### `docker-compose.yml`

```yaml
version: "3.9"

services:
  certbot:
    image: certbot/dns-cloudflare:latest
    container_name: certbot
    restart: unless-stopped
    # 共享证书与日志目录
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/log:/var/log/letsencrypt
      - ./scripts:/scripts:ro
      - ./cloudflare.ini:/cloudflare.ini:ro   # CF token
      - /var/run/docker.sock:/var/run/docker.sock  # 续期后 reload nginx
    env_file: .env
    entrypoint: ["/bin/sh", "/scripts/00_start.sh"]

  nginx:
    image: nginx:alpine
    container_name: nginx
    restart: unless-stopped
    depends_on:
      - certbot           # 只有证书就绪才会启动
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 你指定的四个目录
      - ./nginx/sites-available:/etc/nginx/sites-available
      - ./nginx/sites-enabled:/etc/nginx/sites-enabled
      - ./nginx/snippets:/etc/nginx/snippets
      - ./nginx/www:/var/www
      # 只读挂载证书
      - ./certbot/conf:/etc/letsencrypt:ro
    command: ["nginx", "-g", "daemon off;"]

networks:
  default:
    name: web
```

#### 自动化脚本

##### `scripts/00_start.sh`（容器 PID 1，负责调用另两个脚本）

```bash
#!/bin/sh
set -e

# 1️⃣ 首次启动：若没有证书就阻塞签发
/scripts/10_init_cert.sh

# 2️⃣ 进入 12h 轮询续期循环（永不退出）
exec /scripts/20_renew_loop.sh
```

##### `scripts/10_init_cert.sh`（阻塞式首签脚本）

```bash
#!/bin/sh
set -e

# 读取 .env 中的变量
DOMAIN_CSV="${CERTBOT_DOMAINS:?Need CERTBOT_DOMAINS}"
EMAIL="${CERTBOT_EMAIL:?Need CERTBOT_EMAIL}"
DOMAIN_LIST=$(echo "$DOMAIN_CSV" | tr ',' ' ')
MAIN_DOMAIN=$(echo "$DOMAIN_CSV" | cut -d',' -f1)

if [ -f "/etc/letsencrypt/live/${MAIN_DOMAIN}/fullchain.pem" ]; then
  echo "✓ Existing cert for ${MAIN_DOMAIN} found. Skipping initial issuance."
  exit 0
fi

echo "→ No certificate yet for ${DOMAIN_CSV}, requesting via DNS-01…"
certbot certonly \
  --dns-cloudflare \
  --dns-cloudflare-credentials /cloudflare.ini \
  --dns-cloudflare-propagation-seconds 60 \
  --non-interactive \
  --agree-tos \
  --email "$EMAIL" \
  --server https://acme-v02.api.letsencrypt.org/directory \
  $(printf -- '-d %s ' $DOMAIN_LIST)

echo "✓ Certificate obtained."
```

##### `scripts/20_renew_loop.sh`（12 h 轮询续期 + 自动 reload Nginx）

```bash
#!/bin/sh
set -e

while true; do
  echo "→ Checking certificates for renewal…"
  certbot renew \
    --dns-cloudflare \
    --dns-cloudflare-credentials /cloudflare.ini \
    --non-interactive \
    --deploy-hook "docker kill --signal=HUP nginx || true"
  echo "✓ Renew check done. Sleeping 12h."
  sleep 12h
done
```

#### 配置文件

##### `cloudflare.ini`

```ini
dns_cloudflare_api_token = YOUR_CF_TOKEN
```

**注意**：创建后务必设置权限 `chmod 600 cloudflare.ini`

##### `.env`

```ini
CERTBOT_EMAIL=<EMAIL>
CERTBOT_DOMAINS=*.example.com,example.com
CF_API_TOKEN=YOUR_CF_TOKEN
```

#### 部署步骤

1. **准备 Cloudflare API Token**
   ```bash
   echo "dns_cloudflare_api_token = YOUR_CF_TOKEN" > cloudflare.ini
   chmod 600 cloudflare.ini
   ```

2. **创建脚本目录并设置权限**
   ```bash
   mkdir -p scripts
   chmod +x scripts/*.sh
   ```

3. **填写环境变量**（`.env`）
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入实际的邮箱和域名
   ```

4. **启动服务**
   ```bash
   docker compose up -d
   ```

5. **查看日志**（可选）
   ```bash
   # 查看证书申请过程
   docker compose logs certbot
   
   # 查看 nginx 启动状态
   docker compose logs nginx
   ```

#### 自动化优势对比

| 需求 | 传统方案 | 本方案 |
|------|----------|--------|
| **首次启动** | 必须先创建临时证书，再替换真正证书 | 直接 DNS-01 申请正式证书，一步到位 |
| **多站点 / 新子域** | 每加站点需改脚本、重跑 | 通配符证书覆盖 `*.example.com`，只需新增 server 配置 |
| **续期流程** | 手动或 cron 任务 | Certbot 容器 12h 循环续期，自动 reload Nginx |
| **目录挂载** | 仅证书与 challenge | 额外挂载 `sites-available / enabled / snippets / www`，与传统 Nginx 目录结构一致 |
| **证书验证** | HTTP-01 需要配置验证路径 | DNS-01 验证，无需额外配置 |

部署完成后，我们需要了解如何配置核心的配置片段，这些片段会被多个站点复用，确保配置的一致性和可维护性。

### 4. 配置片段

为了避免重复配置，我们将通用的配置片段提取到 `snippets/` 目录下。这些片段包含了 SSL 安全参数、反向代理通用配置、缓存策略等。

#### `snippets/ssl-params.conf`（SSL 安全配置）

```nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers   'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256';
ssl_prefer_server_ciphers on;
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:50m;
ssl_session_tickets off;

add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

#### `snippets/static-cache.conf`（静态资源缓存配置）

```nginx
# 按扩展名映射不同过期时间
map $uri $static_expires {
    default         0;      # 默认不缓存
    ~*\.(html?|json)$  5m;   # HTML 和 JSON 文件缓存5分钟
    ~*\.css$        7d;   # CSS 文件缓存7天
    ~*\.js$         7d;   # JavaScript 文件缓存7天
    ~*\.(ico|woff2?|ttf|eot|svg)$ 30d; # 图标和字体文件缓存30天
    ~*\.(jpe?g|png|gif|bmp|webp)$ 30d; # 图片文件缓存30天
}

location / {
    try_files $uri $uri/ =404;
    expires $static_expires;
    etag    on;
}
```

#### `snippets/proxy-common.conf`（反向代理通用配置）

```nginx
# ========== 基本代理头部 ==========
proxy_set_header Host              $host;
proxy_set_header X-Real-IP         $remote_addr;
proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host  $host;
proxy_set_header X-Forwarded-Port  $server_port;

# ========== 通用安全头部 ==========
# 防止 MIME 类型嗅探攻击
add_header X-Content-Type-Options            "nosniff" always;
# 防止点击劫持攻击
add_header X-Frame-Options                   "SAMEORIGIN" always;
# XSS 保护（保持向后兼容性）
add_header X-XSS-Protection                  "1; mode=block" always;
# 控制跨域策略文件访问
add_header X-Permitted-Cross-Domain-Policies "none" always;

# ========== 信息隐藏 ==========
proxy_hide_header X-Powered-By;  # 隐藏后端技术信息
proxy_hide_header Server;        # 隐藏服务器版本信息

# ========== 连接优化 ==========
proxy_http_version    1.1;       # 启用 HTTP/1.1，支持 Keep-Alive
proxy_set_header      Connection "";  # 启用连接复用

# ========== 缓冲配置 ==========
proxy_buffering       on;        # 开启代理缓冲，减轻后端压力
proxy_buffer_size     8k;        # 响应头缓冲区大小
proxy_buffers         8 32k;     # 响应体缓冲区数量和大小
proxy_busy_buffers_size 16k;     # 忙碌缓冲区大小

# ========== 超时设置 ==========
proxy_connect_timeout 60s;       # 后端连接超时时间
proxy_read_timeout    300s;      # 后端读取响应超时时间
proxy_send_timeout    60s;       # 向后端发送请求超时时间

# ========== 大文件处理 ==========
proxy_request_buffering off;     # 大文件上传友好
proxy_max_temp_file_size 0;      # 避免临时文件
```

配置片段完成后，我们的部署流程就基本完成了。现在可以开始进行日常的站点管理工作。

## 站点管理

基于通配符证书的自动化方案，站点管理变得非常简单：

| 操作 | 步骤 |
|------|------|
| **新增子域站点** | ① 在 `sites-available/` 复制或新建 `foo.conf`<br>② `ln -s ../sites-available/foo.conf sites-enabled/`<br>③ `docker kill --signal=HUP nginx`<br>**无需申请新证书**，通配符证书自动覆盖 |
| **新增根域站点** | ① 编辑 `.env`，在 `CERTBOT_DOMAINS` 中添加新域名<br>② 重启 certbot：`docker compose restart certbot`<br>③ 创建站点配置并启用 |
| **下线站点** | `rm sites-enabled/foo.conf && docker kill --signal=HUP nginx` |
| **修改配置** | 编辑 `sites-available/foo.conf` → `docker kill --signal=HUP nginx` |

## 日常管理命令

```bash
# 新增站点配置（以 blog.example.com 为例）
ln -s /etc/nginx/sites-available/blog.conf /etc/nginx/sites-enabled/

# 平滑重载 nginx 配置
docker kill --signal=HUP nginx

# 手动触发证书续期检查（可选）
docker compose exec certbot certbot renew --dry-run

# 查看证书状态
docker compose exec certbot certbot certificates
```

---

## 可选增强配置

以下是一些可以进一步优化 Nginx 服务和安全性的可选配置。

### 1. HTTP/3 (QUIC) 支持

如果您的 Nginx 版本 >= 1.25，可以启用 HTTP/3 来提升性能，尤其是在网络不稳定的情况下。

```nginx
server {
    # 在 listen 443 ssl http2; 下方添加
    listen 443 quic reuseport; # 启用 QUIC

    # ... 其他 server 配置 ...

    ssl_early_data on; # 允许TLS 1.3早期数据，减少握手延迟

    # 添加 Alt-Svc 头部，告知客户端 HTTP/3 可用
    add_header Alt-Svc 'h3=":443"; ma=86400';
    # 如果您有多个支持 QUIC 的端口，可以相应修改
}
```
**注意**: 启用 HTTP/3 需要 Nginx 编译时包含 `--with-http_v3_module`，并且服务器需要开放相应的 UDP 端口 (默认为 443/udp)。

### 2. Nginx 状态监控

通过 `stub_status` 模块可以获取 Nginx 的基本运行状态，方便监控。

在您的 `nginx.conf` (通常是主配置文件，或者在 `conf.d/` 下新建一个配置) 的 `http` 块内添加：
```nginx
server {
    listen 127.0.0.1:8080; # 仅允许本地访问监控页面
    server_name localhost;

    location /nginx_status {
        stub_status on;
        allow 127.0.0.1;    # 允许来自本地的访问
        deny all;           # 禁止其他所有IP访问
    }
}
```

之后，您可以通过 `curl http://127.0.0.1:8080/nginx_status` 查看状态。

> **版本控制建议**
> 把 `nginx/` 与脚本、Compose file 全部纳入 Git，配合 CI/CD（如 GitHub Actions、Ansible）完成自动构建与回滚。

---

## 进阶配置说明

如果您需要更详细的配置说明和高级功能，可以参考以下文档：

- [📖 静态站点配置详解](./docs/static-site-config.md) - 包含更多静态站点配置变体和优化技巧
- [📖 反向代理配置详解](./docs/reverse-proxy-config.md) - 包含负载均衡、健康检查等高级代理功能
- [📖 SSL 安全配置详解](./docs/ssl-security-config.md) - 包含更严格的安全配置和最佳实践
- [📖 性能优化配置详解](./docs/performance-optimization.md) - 包含高并发优化和缓存策略