# Nginx 最佳实践与 Docker-化部署手册 ✨

## 前言

欸欸…初次接触 nginx 的时候，咱对那些繁杂的配置真的是头疼得不行呢 QAQ 于是就想整理一个适合咱自己用的最佳实践，这样就有了这篇小小的文档啦~ （偷瞄）

> 适用场景 w
> * 多子域静态站点（博客 / 文档 / SPA 等）
> * 多容器业务（Nextcloud、Gitea…）共用一台服务器，通过子域或路径反向代理  
> * 自动申请与续签 Let's Encrypt 证书

呜呜…希望能帮到各位前辈呢~

---

## 目录

TODO: 文档目录（咱会慢慢补充的 w）

---

## 部署流程

整个部署流程可以大致分成以下几个部分：

TODO: 部署流程图（画图什么的咱还在学习中…）

下面，咱来详细说明各流程细节吧~ （脸红）

### 1. 创建目录结构

部署 nginx 的第一步是创建对应的目录结构呢~

咱在参考 [Debian/Ubuntu 打包团队的惯例](https://www.kevinhooke.com/2025/01/02/configuring-nginx-virtual-hosts-with-sites-available-sites-enabled/) 和在 docker 中部署 nginx + certbot 的[一篇博客](https://dev.to/marrouchi/the-challenge-about-ssl-in-docker-containers-no-one-talks-about-32gh)后，最终得到的目录结构如下：

```
nginx/
├── docker-compose.yml      # Docker Compose 配置
├── docker-compose.yml      # Docker Compose 配置
├── docker-compose.yml      # Docker Compose 配置
├── docker-compose.yml      # Docker Compose 配置
├── sites-available/        # 所有站点配置
├── sites-enabled/          # 软链指向启用站点
├── snippets/               # 公共片段
│   ├── ssl-params.conf     # SSL 安全配置片段
│   ├── proxy-common.conf   # 反向代理通用配置片段
│   ├── proxy-security-strict.conf    # 私有服务严格安全配置
│   ├── proxy-security-moderate.conf  # 公开服务适度安全配置
│   └── proxy-nextcloud.conf          # Nextcloud 专用配置
├── certbot/                # 共享卷：ACME 挑战与证书
│   ├── conf/               # /etc/letsencrypt
│   └── www/                # /.well-known/acme-challenge
└── www/                    # 静态文件目录
```

这些目录和脚本的作用咱会在部署过程中慢慢介绍的 w 现在，咱们先创建这些目录和文件：

```bash
mkdir -p nginx/{sites-available,sites-enabled,snippets,certbot/{conf,www},www}
touch nginx/docker-compose.yml nginx/99-autoreload.sh

# 创建配置文件模板
touch nginx/snippets/{ssl-params.conf,proxy-common.conf,proxy-security-strict.conf,proxy-security-moderate.conf,proxy-nextcloud.conf}
```

### 2. 站点配置

目录结构创建完毕后就可以开始编辑具体的站点配置了呢~

在 `sites-available/` 目录下创建站点配置文件。当站点配置编辑完毕后，就可以软链接到 `sites-enabled/` 目录下，表示配置已启用。而 nginx.conf 中仅包含 `sites-enabled/*` 的配置，这样就能保证只有配置好的文件才会被 `nginx` 解析，同时后续如果想要下线某站点，只需要删除软链接即可 w

```
nginx/
└── conf/
    ├── sites-available/        # 所有站点配置
        ├── site1.conf
        ├── site2.conf
        └── ...
    └── sites-enabled/          # 软链指向启用站点
        ├── site1.conf          # 软链到 sites-available/site1.conf，表示已启用的配置
        └── ...
```

接下来来讲下站点配置的内容吧~

配置内容会因需求不同而大相径庭呢。就咱个人而言，咱使用 nginx 主要有两个目的：

1. 静态站点托管
2. docker 服务反向代理

因此，咱编写了以下两种配置：

#### 1. 静态站点托管

```nginx
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name <domain.com>;

    root /var/www/<site-directory>;

    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/<domain.com>/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/<domain.com>/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数，见下文

    # ACME Challenge 路径
    # Let's Encrypt (Certbot) 用这个路径通过 HTTPS 验证域名所有权
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;    # 这个路径要和 docker-compose.yml 里 certbot 卷挂载的路径一致
        default_type "text/plain";
        allow all;                # 必须允许所有访问，LCS 服务器才能进来
    }

    # 高并发优化选项
    gzip_static on; # 前提是静态文件已经预先用 gzip 压缩过了，并带 .gz 后缀
    open_file_cache max=10000 inactive=30s;
    sendfile on;
    tcp_nopush on;

    # 统一资源缓存策略
    include snippets/static-cache.conf; # 具体的缓存规则在这个文件里定义

    # SPA (单页应用) 路由回退
    # try_files $uri $uri/ /index.html;

    # 日志路径
    access_log /var/log/nginx/<domain.com>.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/<domain.com>.error.log warn;
}

# HTTP 强制跳转 HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name <domain.com>;

    # ACME Challenge 路径
    # Let's Encrypt 进行 HTTP-01 验证时需要这个路径
    # 关键：这个 location 块必须放在下面的 return 301 前面，不然验证请求会被重定向，导致失败
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;    # 和 HTTPS server 块里的路径保持一致
        default_type "text/plain";
        allow all;
    }

    # 其他所有 HTTP 请求，一律 301 重定向到 HTTPS
    return 301 https://$host$request_uri;
}
```

部署时，需要先替换占位符：

* `<domain.com>`: 换成真实域名。
* `<site-directory>`: 换成站点文件实际的根目录。

下面，咱来讲解一下这部分配置的内容 w

##### ssl 配置解析

先来看 **ssl 配置** 相关的内容呢~

```nginx
server {
    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/<domain.com>/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/<domain.com>/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数，见下文

    # ACME Challenge 路径
    # Let's Encrypt (Certbot) 用这个路径通过 HTTPS 验证域名所有权
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;    # 这个路径要和 docker-compose.yml 里 certbot 卷挂载的路径一致
        default_type "text/plain";
        allow all;                # 必须允许所有访问，LCS 服务器才能进来
    }
}

server {
    # ACME Challenge 路径
    # Let's Encrypt 进行 HTTP-01 验证时需要这个路径
    # 关键：这个 location 块必须放在下面的 return 301 前面，不然验证请求会被重定向，导致失败
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;    # 和 HTTPS server 块里的路径保持一致
        default_type "text/plain";
        allow all;
    }
}
```

首先是 `ssl_certificate` 和 `ssl_certificate_key` 这两个配置项，它们分别指向 ssl 证书和私钥的路径。它们实际的文件内容由 certbot 进行管理，因此咱们只需要指定路径即可 w

然后是两个 `server` 块中的 ACME Challenge 部分，这部分对应了配置中的 `location ^~ /.well-known/acme-challenge/` 配置项。其中主要内容就是指定 ACME Challenge 的请求路径，并允许所有访问（在 `http` 块中还用于阻止 ACME 验证流量 301 重定向到 HTTPS）。
如果没有重定向 `root` 请求路径的话，nginx 就会把请求落到 `www/var/<site-directory>/` ,进而导致 404 呢~

最后是 SSL 通用参数部分，这部分内容会包含在 `include snippets/ssl-params.conf` 配置项中。具体内容如下：

###### `snippets/ssl-params.conf`（SSL 通用安全参数）

```nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers   'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256';
ssl_prefer_server_ciphers on;
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:50m;
ssl_session_tickets off;

add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
add_header X-Content-Type-Options "nosniff";
add_header X-Frame-Options "SAMEORIGIN";
add_header Referrer-Policy "strict-origin-when-cross-origin";
```

以下是 gpt 对各个配置项的解释，咱觉得很有用所以搬过来了 w

| 指令 | 做什么 | 为什么这么配 |
|------|--------|--------------|
| `ssl_protocols TLSv1.2 TLSv1.3;` | 仅允许 TLS 1.2 与 1.3 | <br>**淘汰旧版协议**：TLS 1.0/1.1 在 2021 年起就被主要浏览器和云厂商全面弃用，残留价值只在于极老旧客户端。关闭它们可防御 BEAST、POODLE 等已知攻击。<br>**保留 TLS 1.2**：仍覆盖 >99 % 客户端（含 Android 7、Win 7/8/10 旧版 IE）。<br>**开启 TLS 1.3**：更快握手、内建前向保密 (PFS)、固定安全套件，已由所有现代浏览器支持。 |
| `ssl_ciphers 'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256';` | 指定可用的 **TLS 1.3** 密码套件顺序 | TLS 1.3 只允许四个官方套件，里头去掉不常用的 `TLS_AES_128_CCM_*`，留下 3 个主流：<br>• `TLS_AES_256_GCM_SHA384` —— GCM + SHA-384，适合硬件加速 <br>• `TLS_CHACHA20_POLY1305_SHA256` —— 移动端无 AES-NI 时更快 <br>• `TLS_AES_128_GCM_SHA256` —— 与上兼容、密钥更短 <br>顺序对 TLS 1.3 实际无影响（浏览器自选），但写明能让配置更显式，也同时约束 **TLS 1.2** 的协商范围（nginx 在 TLS 1.2 时仍会从同一行解析套件）。 |
| `ssl_prefer_server_ciphers on;` | 对 **TLS 1.2 及以下** 强制使用服务器端给出的套件优先级 | 阻止客户端选到弱套件（如 3DES）。TLS 1.3 协议层自带固定套件列表，不受此指令影响。 |
| `ssl_session_timeout 1d;` | 会话 ID（传统 Session Cache）在内存中保活 1 天 | 让同一用户 24 小时内重新建立连接时可复用主密钥，减少握手 CPU & RTT；时间过长会加大密钥被窃取后的利用窗口，一天属于兼顾性能与安全的折中。 |
| `ssl_session_cache shared:SSL:50m;` | 建一个 50 MiB 的共享内存区跨 worker 进程保存会话 | 大约能容纳 4–8 万条会话记录（取决于编译选项）；在多进程 nginx 中共享可显著提升复用率。 |
| `ssl_session_tickets off;` | **禁用**会话票据 (Session Ticket) | Session Ticket 会把主密钥加密后下发给客户端，若票据密钥轮换/保存不当，会削弱前向保密；许多现代浏览器都已支持更安全的 0-RTT/PSK 方案，所以小到中型站点直接关掉最省事。若你需要 Ticket(如高并发握手场景)，应配合自动轮换 `ssl_ticket_key`. |
| `add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;` | **HSTS**，强制客户端未来 2 年（≈63072000 秒）只用 HTTPS 访问本域及全部子域，并宣告可加入浏览器 **preload list** | 避免中间人降级到 HTTP；注意：一旦上线并被 preload，未来无法轻易回退到 HTTP。 |
| `add_header X-Content-Type-Options "nosniff";` | 禁止浏览器 **MIME 嗅探** | 防止把纯文本/脚本误当成可执行资源，引发 XSS。 |
| `add_header X-Frame-Options "SAMEORIGIN";` | 仅允许本域把页面嵌入 `<iframe>` | 阻止点击劫持 (Clickjacking)。若需跨域嵌入可用 CSP 的 `frame-ancestors` 取代。 |
| `add_header Referrer-Policy "strict-origin-when-cross-origin";` | 控制 **Referer** 头 | 向同站点请求发送完整路径（便于统计），跨站点仅发送协议+域（保护隐私），兼顾可观测性与隐私。 |

针对其调整场景，gpt 是这样说的：

| 场景 | 建议 |
|------|------|
| **必须兼容非常老的客户端**（如 Win XP、Java 6） | 加回 `TLSv1.1` 并开放 `ECDHE-RSA-AES128-SHA` 等旧套件，但要权衡安全，建议单独起一个回退虚拟机或端口。 |
| **高性能 / 边缘节点** 想开启 Session Ticket | 把 `ssl_session_tickets on;` 并定时轮换 `ssl_ticket_key`（或让 nginx 自动生成并热重载）。 |
| **想减少丢包场景下的握手 RTT** | 开 TLS 1.3 0-RTT：`ssl_early_data on;` 并在应用层检查重放风险。 |
| **需要 CSP、Permissions-Policy 等更细粒度防护** | 在此文件或单独 snippet 里添加 `Content-Security-Policy`、`Permissions-Policy` 头以控制脚本执行、API 使用等。 |

**小结**
`ssl-params.conf` 这几行就是 **现代 HTTPS 安全基线** 呢：

1. **只留 TLS 1.2/1.3 + 强套件**——抗降级&弱加密攻击。
2. **合理的会话复用与 ticket 策略**——兼顾性能与前向保密。
3. **安全响应头**——提升浏览器端防御层级。

##### 高并发优化选项解析

```nginx
server {
    # 高并发优化选项
    gzip_static on; # 前提是静态文件已经预先用 gzip 压缩过了，并带 .gz 后缀
    open_file_cache max=10000 inactive=30s;
    sendfile on;
    tcp_nopush on;
}
```

接下来是 **高并发优化选项** 部分的讲解，依旧是 gpt 的解释（咱觉得解释得很清楚所以偷懒了 w）：

| 指令 | 做什么 | 工作原理 | 适用场景 & 注意点 |
|------|--------|---------|------------------|
| **`gzip_static on;`** | 如果客户端在 `Accept-Encoding` 里声明支持 `gzip`，且磁盘上存在同名文件 `xxx.css.gz`，Nginx 直接把该压缩文件发给客户端，并在响应头加 `Content-Encoding: gzip`。**不再临时压缩**。 | 把"压缩"这件 CPU 密集型工作前移到构建阶段（CI/CD or webpack/rollup 等），运行时仅做**零开销的文件读取**。 | - 前提：各位已在打包流程里生成 `.gz` 文件（或更优的 Brotli）。<br>- 不对动态请求生效（如 PHP/FastCGI）。<br>- 若启用 HTTP/2 + TLS，浏览器仍愿意收 gzip；若想给新浏览器用 Brotli，可在同目录放 `.br` 并用 `brotli_static on;`（需编译模块或用官方 `nginx:mainline-alpine`）。 |
| **`open_file_cache max=10000 inactive=30s;`** | 让 Nginx 把最近访问过的文件句柄、大小、时间戳等元数据放进共享缓存：<br>• *最多* 缓 10000 项；<br>• 若 30 秒内未再访问，则从缓存淘汰。 | Linux 打开同一文件需要系统调用 `open()` + `stat()`；在热点静态站点里，这部分可能占到**几十 % 的 CPU**。缓存后，Nginx 直接复用文件描述符或元数据，少一次或多次系统调用。 | - "10000" 并不大，占用内存 ~1–2 MB，可按站点热文件数酌情加大。<br>- 只建议对静态文件目录开启；如果各位有大量短暂文件（如上传存储）可单独写在 `location` 块里排除缓存。 |
| **`sendfile on;`** | 启用 Linux `sendfile(2)` 零拷贝：从磁盘缓存页直接写入套接字缓冲区，不再绕经用户态。 | 普通流程：磁盘 → 内核→ 用户态 (Nginx) → 再回内核 → NIC。<br>启用后：磁盘缓存页地址直接映射到 NIC DMA，省掉两次内核/用户拷贝。 | - 对大文件（图片、视频、JS/CSS）效果最明显。<br>- 不会影响动态 upstream 响应（它们本来是在内存中）。<br>- 若使用 SSL，需要内核 4.6+ 的 `sendfile` + `TLS offload`，否则 Nginx 会自动降级为 `writev()`；现代发行版/官方 Nginx 镜像都已支持。 |
| **`tcp_nopush on;`** | 与 `sendfile` 配套：在 **发送最后一个包之前**，内核尽量把响应头和若干文件数据**拼成一个满大小 MSS 报文**再发，减少 TCP 包数量。 | 利用 Linux `TCP_CORK` 套接字选项：先"塞住" (cork) 输出，让内核聚合数据；当缓冲区满或关闭时"一次性"发出。 | - 只有当 `sendfile on` 时才建议启用。<br>- 对启用了 **HTTP/2** 的端口无影响（HTTP/2 在同一个 TLS 流里会做更高级的帧合并）。<br>- 极少数对极低延迟敏感的长轮询/实时应用可考虑关闭。 |

##### 缓存策略解析

```nginx
server {
    # 统一资源缓存策略
    include snippets/static-cache.conf; # 具体的缓存规则在这个文件里定义
}
```

站点中各个文件的缓存策略都放到了 `snippets/static-cache.conf` 文件中，具体内容如下：

###### `snippets/static-cache.conf`（统一缓存规则）

```nginx
# 先按扩展名映射不同过期时间
map $uri $static_expires {
    default         0;      # 默认不缓存或极短缓存 (例如 API 动态内容)
    ~*\\.(html?|json)$  5m;   # HTML 和 JSON 文件缓存5分钟
    ~*\\.css$        7d;   # CSS 文件缓存7天
    ~*\\.js$         7d;   # JavaScript 文件缓存7天
    ~*\\.(ico|woff2?|ttf|eot|svg)$ 30d; # 图标和字体文件缓存30天
    ~*\\.(jpe?g|png|gif|bmp|webp)$ 30d; # 图片文件缓存30天
}

location / {
    try_files $uri $uri/ =404; # 对于目录，尝试 $uri/index.html (如果 autoindex off)
    expires $static_expires;
    etag    on; # 开启 ETag，配合 Cache-Control 实现更精细的缓存验证
}
```

静态文件的缓存配置一目了然，没什么好讲的呢 w 它们的作用是针对不同文件的请求，在响应中添加 `max-age` 值不同（如 html 文件 5 分钟，css 文件 7 天，图片文件 30 天）的 `Cache-Control` 头，从而实现不同的缓存策略。

`location /` 块中给出了默认应用（非 SPA）的文件请求处理模式，即找不到文件或对应目录的 index 后直接返回 404。同时还开启了 `etag` 选项，配合 `Cache-Control` 头实现更精细的缓存验证。

##### SPA 配置解析

```nginx
server {
    # SPA (单页应用) 路由回退
    # try_files $uri $uri/ /index.html;
}
```

和先前默认处理方式不同的是，SPA 应用在无法找到文件时返回的是 `index.html` 文件。确保具体请求能由客户端路由接管，避免 SPA 的深层链接和刷新功能无法正常工作 w

#### 2. Docker 服务反向代理

呜呜…Docker 服务反向代理的配置会更复杂一些呢：

```nginx
# 上游服务器定义
upstream nextcloud { server 127.0.0.1:9000; } # 假设 Nextcloud 服务在本机的 9000 端口
upstream gitea { server 127.0.0.1:9001; }   # 假设 Gitea 服务在本机的 9001 端口

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name <domain.com>;

    # SSL 证书路径 (Certbot 管理)
    ssl_certificate     /etc/letsencrypt/live/<domain.com>/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/<domain.com>/privkey.pem;
    include snippets/ssl-params.conf; # 通用 SSL 安全参数

    # ACME Challenge 路径 (HTTPS 验证)
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        default_type "text/plain";
        allow all;
    }

    # 日志路径
    access_log /var/log/nginx/<domain.com>.access.log main buffer=32k flush=5s;
    error_log  /var/log/nginx/<domain.com>.error.log warn;

    # Nextcloud（仅内网访问）
    location ^~ /nextcloud/ {
        # IP访问控制
        allow 10.0.0.0/8;
        allow ***********/16;
        deny all;

        # 反向代理配置
        proxy_pass http://nextcloud/;  # 注意尾部的斜杠，它会移除匹配到的 /nextcloud/ 部分
        include snippets/proxy-common.conf;
        include snippets/proxy-nextcloud.conf;  # Nextcloud 专用配置
    }

    # Gitea（公网访问）
    location ^~ /gitea/ {
        proxy_pass http://gitea/; # 注意尾部的斜杠
        include snippets/proxy-common.conf;
        include snippets/proxy-security-moderate.conf;  # 公开服务安全配置

        # WebSocket支持 (Gitea 可能需要)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location / { return 404; } # 其他未匹配路径返回 404
}
```

如果需要添加新的 docker 服务，只需要添加对应的 `upstream` 块和 `location` 块即可 w

---

## 站点管理

| 操作 | 步骤 |
|------|------|
| **新增站点** | ① 在 `sites-available/` 复制或新建 `foo.conf`<br>② `ln -s` 到 `sites-enabled/`<br>③ 添加域名到 `domains=( … )`，执行 `init-letsencrypt.sh` **或**<br>   手动：`docker compose run --rm certbot certonly -w /var/www/certbot -d foo.bar --email you@xx --agree-tos`<br>④ `docker compose exec nginx nginx -s reload` |
| **下线站点** | `rm sites-enabled/foo.conf && docker compose exec nginx nginx -s reload` |
| **修改配置** | 编辑 `sites-available/foo.conf` → `docker compose exec nginx nginx -t && nginx -s reload` |

---

## 完整配置文件内容

为方便部署，以下是各配置文件的完整内容，各位可以直接复制使用：

#### `snippets/ssl-params.conf`

```nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers   'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256';
ssl_prefer_server_ciphers on;
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:50m;
ssl_session_tickets off;

add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
add_header X-Content-Type-Options "nosniff";
add_header X-Frame-Options "SAMEORIGIN";
add_header Referrer-Policy "strict-origin-when-cross-origin";
```

#### `snippets/static-cache.conf`

```nginx
# 按扩展名映射不同过期时间
map $uri $static_expires {
    default         0;      # 默认不缓存
    ~*\\.(html?|json)$  5m;   # HTML 和 JSON 文件缓存5分钟
    ~*\\.css$        7d;   # CSS 文件缓存7天
    ~*\\.js$         7d;   # JavaScript 文件缓存7天
    ~*\\.(ico|woff2?|ttf|eot|svg)$ 30d; # 图标和字体文件缓存30天
    ~*\\.(jpe?g|png|gif|bmp|webp)$ 30d; # 图片文件缓存30天
}

location / {
    try_files $uri $uri/ =404;
    expires $static_expires;
    etag    on;
}
```

#### `snippets/proxy-common.conf`

```nginx
# ========== 基本代理头部 ==========
proxy_set_header Host              $host;
proxy_set_header X-Real-IP         $remote_addr;
proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host  $host;
proxy_set_header X-Forwarded-Port  $server_port;

# ========== 通用安全头部 ==========
add_header X-Content-Type-Options            "nosniff" always;
add_header X-Frame-Options                   "SAMEORIGIN" always;
add_header X-XSS-Protection                  "1; mode=block" always;
add_header X-Permitted-Cross-Domain-Policies "none" always;

# ========== 信息隐藏 ==========
proxy_hide_header X-Powered-By;  # 隐藏后端技术信息
proxy_hide_header Server;        # 隐藏服务器版本信息

# ========== 连接优化 ==========
proxy_http_version    1.1;       # 启用 HTTP/1.1，支持 Keep-Alive
proxy_set_header      Connection "";  # 启用连接复用

# ========== 缓冲配置 ==========
proxy_buffering       on;        # 开启代理缓冲，减轻后端压力
proxy_buffer_size     8k;        # 响应头缓冲区大小
proxy_buffers         8 32k;     # 响应体缓冲区数量和大小
proxy_busy_buffers_size 16k;     # 忙碌缓冲区大小

# ========== 超时设置 ==========
proxy_connect_timeout 60s;       # 后端连接超时时间
proxy_read_timeout    300s;      # 后端读取响应超时时间
proxy_send_timeout    60s;       # 向后端发送请求超时时间

# ========== 大文件处理 ==========
proxy_request_buffering off;     # 大文件上传友好
proxy_max_temp_file_size 0;      # 避免临时文件
```

---

QAQ 咱的小小文档就到这里啦~ 希望能帮到各位前辈，如果有什么问题或者建议，请随时告诉咱哟 w （脸红）