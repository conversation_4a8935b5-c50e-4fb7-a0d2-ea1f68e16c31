services:
  nginx:
    image: nginx:latest
    container_name: nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # Nginx 配置
      - ./sites-available/:/etc/nginx/sites-available/
      - ./sites-enabled/:/etc/nginx/sites-enabled/
      - ./snippets/:/etc/nginx/snippets/
      - ./nginx.conf:/etc/nginx/nginx.conf

      # Let’s Encrypt 持久化
      - ./conf/certbot/conf:/etc/letsencrypt
      - ./conf/certbot/www:/var/www/certbot

      # 静态文件根目录
      - ./www:/var/www

      # ↙︎ 自动执行脚本（按字母顺序，00 最先跑）
      - ./scripts/00-init-ssl.sh:/docker-entrypoint.d/00-init-ssl.sh:ro
      # ↙︎ 你的热重载脚本，维持原样
      - ./scripts/99-autoreload.sh:/docker-entrypoint.d/99-autoreload.sh:ro

  certbot:
    image: certbot/certbot
    restart: unless-stopped
    volumes:
      - ./conf/certbot/conf:/etc/letsencrypt
      - ./conf/certbot/www:/var/www/certbot
    # 12 小时续期轮询 + 成功后通知 Nginx 热加载
    entrypoint: >
      /bin/sh -c '
        trap exit TERM;
        while :; do
          certbot renew --webroot -w /var/www/certbot \
            --deploy-hook "nginx -s reload || true";
          sleep 12h & wait $${!};
        done'
