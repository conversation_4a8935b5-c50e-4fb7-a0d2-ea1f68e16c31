user nginx;

worker_processes  auto;

events {
    worker_connections  10240;
    # 对 Linux 5.x/epoll 开启优化
    use epoll;
    multi_accept on;
}

###################################
#  HTTP（80/443）                  #
###################################
http {
    # 通用优化 ------------------------------------------------------------
    include       mime.types;
    default_type  application/octet-stream;

    # 安全相关
    server_tokens off;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff always;

    # 传输优化
    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65s;
    types_hash_max_size 4096;

    # 压缩
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 1k;
    gzip_types text/plain text/css application/json application/javascript image/svg+xml;

    # 日志
    log_format  main  '$remote_addr [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;
    error_log   /var/log/nginx/error.log   warn;

    # SSL 通用片段（certbot/手动均可）
    include /etc/nginx/snippets/*.conf;

    # 把真正的虚拟主机文件独立出去
    include /etc/nginx/conf.d/*.conf;
}

###################################
#  STREAM（TCP/UDP 转发）          #
###################################
stream {
    # 流量日志（可选）
    log_format stream_main '$remote_addr [$time_local] '
                           '$protocol $status $bytes_sent $bytes_received '
                           '$session_time $upstream_addr';
    access_log /var/log/nginx/stream-access.log stream_main;

    include /etc/nginx/stream.d/*.conf;
}