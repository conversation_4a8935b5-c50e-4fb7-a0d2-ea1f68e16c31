#!/usr/bin/env bash
set -euo pipefail

# -------------------- 变量，可按需覆写 --------------------
NGINX_CONF_DIR="${NGINX_CONF_DIR:-/etc/nginx/sites-enabled}"
LE_LIVE_DIR="${LE_LIVE_DIR:-/etc/letsencrypt/live}"
CHECK_SECONDS="${CHECK_SECONDS:-86400}"   # 24h

# -------------------- 函数 --------------------
log() { echo "[init-ssl] $*"; }

need_temp_cert() {
  local cert="$1"
  # 文件不存在 ⇒ 需要； 或者证书在 CHECK_SECONDS 内过期 ⇒ 需要
  [[ ! -f "$cert" ]] && return 0
  ! openssl x509 -checkend "$CHECK_SECONDS" -noout -in "$cert"
}

gen_temp_cert() {
  local domain="$1" cert_dir="$2"
  mkdir -p "$cert_dir"
  openssl req -x509 -nodes -newkey rsa:2048 \
    -subj "/CN=$domain" \
    -days 1 \
    -keyout "$cert_dir/privkey.pem" \
    -out "$cert_dir/fullchain.pem" \
    >/dev/null 2>&1
  log "generated 1-day self-signed cert for $domain"
}

# -------------------- 主流程 --------------------
log "scanning $NGINX_CONF_DIR …"
mapfile -t ssl_confs < <(grep -Rls "listen .*443.*ssl" "$NGINX_CONF_DIR" || true)

declare -A domains_seen
for conf in "${ssl_confs[@]}"; do
  # 提取所有 server_name（支持多个，用空格分隔）
  for domain in $(grep -Po 'server_name\s+\K[^;]+' "$conf" | tr ' ' '\n'); do
    domains_seen["$domain"]=1
  done
done

for domain in "${!domains_seen[@]}"; do
  cert_dir="$LE_LIVE_DIR/$domain"
  cert_file="$cert_dir/fullchain.pem"

  if need_temp_cert "$cert_file"; then
    gen_temp_cert "$domain" "$cert_dir"
  else
    log "valid cert already present for $domain"
  fi
done

log "done."
