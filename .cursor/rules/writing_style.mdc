---
description:
globs:
alwaysApply: false
---
# 博客语言风格总结与描述

- **受众定位清晰、语气亲切**
  通篇以第一人称写作，强调"我在实践中的经验""个人最佳实践"，让读者感觉作者就在耳边聊天，语气平易近人但不失专业。

- **结构化极强、Markdown 语法娴熟**
  采用成体系的层级标题（H2/H3）、引用块、分隔线、代码块和有序 / 无序列表，方便浏览器或编辑器直接渲染成易读页面。

- **信息浓缩且可执行**
  每个步骤都配合目录树、脚本或命令行示例，读者复制即可运行；同时提供来源链接佐证，增加可信度。

- **中英文夹杂、术语精准**
  关键技术词汇（Docker Compose、certbot、reverse proxy…）直接使用英文，中文负责解释说明，保持术语一致性。

- **"先给场景，再给方案"**
  开头用 > 适用场景 概括问题域，然后循序渐进展开解决方案，逻辑清晰。

---

### 仿写一段博客（延续该文风）

#### 3. 自动重载与日志管理

在容器里改完 `nginx.conf` 后，最怕的就是忘记 reload。与其手动敲命令，不如让脚本替我们盯着配置文件，检测到变动立即重载，并顺便把访问日志滚动归档。

```
# conf/99-autoreload.sh
#!/usr/bin/env bash
set -eu

inotifywait -mq -e close_write,moved_to,create /etc/nginx \
| while read -r _; do
    echo "[autoreload] $(date '+%F %T') config changed, reloading..."
    nginx -s reload
done
```

> **为什么用 inotify？**
> * 轻量：内核级事件，无需轮询
> * 实时：毫秒级触发，运维再也不用担心"我到底 reload 了没？"

同时，我们在 `docker-compose.yml` 里添加一个 logrotate 容器，实现日志日切，示例配置如下：

```yaml
services:
  logrotate:
    image: nickblaine/logrotate
    volumes:
      - ./logs/nginx:/var/log/nginx
      - ./conf/logrotate/nginx:/etc/logrotate.d/nginx
    restart: unless-stopped
```

如此一来，配置改动秒生效，日志文件也不会无限膨胀 —— "自动化即安心"。
